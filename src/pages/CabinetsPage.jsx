import { useRef, useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination } from 'swiper/modules';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { getCabinetsData, getFooterData } from '../../database/api-client.js';
import { getImageURL } from '../config/api.js';
import Navbar from '../components/Navbar';
import MobileLayout from '../components/mobile/MobileLayout';
import MobileCabinetsPage from '../components/mobile/MobileCabinetsPage';
import useIsMobile from '../hooks/useIsMobile';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

const CabinetsPage = () => {
  const isMobile = useIsMobile();

  // جميع الـ hooks يجب أن تكون في الأعلى
  const sectionRef = useRef(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [lightboxImage, setLightboxImage] = useState(null);
  const [modalImageIndex, setModalImageIndex] = useState(0);
  const isInView = useInView(sectionRef, { once: true, threshold: 0.1 });
  const [cabinets, setCabinets] = useState([]);
  const [whatsappNumber, setWhatsappNumber] = useState('');
  const [socialMedia, setSocialMedia] = useState([]);

  // إذا كان الجهاز محمول، اعرض النسخة المحمولة
  if (isMobile) {
    return (
      <>
        <Helmet>
          <title>الخزائن - عجائب الخبراء</title>
          <meta name="description" content="اكتشف تشكيلتنا المميزة من الخزائن الأنيقة والعملية في عجائب الخبراء" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        </Helmet>

        <MobileLayout>
          <MobileCabinetsPage />
        </MobileLayout>
      </>
    );
  }

  // قراءة البيانات من قاعدة البيانات عند تحميل المكون
  useEffect(() => {
    const loadCabinetsData = async () => {
      try {
        const cabinetsData = await getCabinetsData();
        console.log('Loaded cabinets data:', cabinetsData);
        if (cabinetsData && cabinetsData.length > 0) {
          // تحويل البيانات من قاعدة البيانات إلى التنسيق المطلوب
          const formattedCabinets = cabinetsData.map(cabinet => ({
            id: cabinet.id,
            title: cabinet.title,
            description: cabinet.description,
            // إذا لم تكن هناك فئة محددة، استخدم 'modern-cabinets' كافتراضي
            category: cabinet.category_slug || 'modern-cabinets',
            images: cabinet.images?.map(img => {
              const imageUrl = typeof img === 'string' ? img : (img.image_url || img);
              return getImageURL(imageUrl);
            }) || []
          }));
          console.log('Formatted cabinets:', formattedCabinets);
          setCabinets(formattedCabinets);
        } else {
          setCabinets([]);
        }
      } catch (error) {
        console.error('Error loading cabinets data:', error);
        setCabinets([]);
      }
    };

    const loadFooterData = async () => {
      try {
        const footerData = await getFooterData();
        if (footerData) {
          // تحميل رقم الواتساب
          if (footerData.whatsapp) {
            setWhatsappNumber(footerData.whatsapp);
          } else if (footerData.contactInfo) {
            const phoneContact = footerData.contactInfo.find(contact => contact.icon === 'ri-phone-line');
            if (phoneContact) {
              setWhatsappNumber(phoneContact.text);
            }
          }
          // تحميل وسائل التواصل الاجتماعي
          if (footerData.socialMedia && footerData.socialMedia.length > 0) {
            setSocialMedia(footerData.socialMedia);
          }
        }
      } catch (error) {
        console.error('Error loading footer data:', error);
      }
    };

    loadCabinetsData();
    loadFooterData();
  }, []);

  // فئات الخزائن
  const categories = [
    { id: 'all', name: 'جميع الخزائن', icon: 'ri-apps-line' },
    { id: 'modern-cabinets', name: 'عصري', icon: 'ri-building-line' },
    { id: 'classic-cabinets', name: 'كلاسيكي', icon: 'ri-home-heart-line' },
    { id: 'wardrobe-cabinets', name: 'خزائن ملابس', icon: 'ri-shirt-line' }
  ];

  const filteredCabinets = selectedCategory === 'all'
    ? cabinets
    : cabinets.filter(cabinet => cabinet.category === selectedCategory);

  // دالة للحصول على ألوان المنصات
  const getBackgroundClass = (platform) => {
    const styles = {
      instagram: "bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600",
      tiktok: "bg-black hover:bg-gray-800",
      snapchat: "bg-yellow-500 hover:bg-yellow-600",
      twitter: "bg-black hover:bg-gray-800",
      whatsapp: "bg-green-500 hover:bg-green-600",
      facebook: "bg-blue-600 hover:bg-blue-700",
      youtube: "bg-red-600 hover:bg-red-700",
      linkedin: "bg-blue-700 hover:bg-blue-800"
    };
    return styles[platform] || "bg-gray-600 hover:bg-gray-700";
  };

  // دالة لفتح WhatsApp مع رابط المنتج
  const openWhatsApp = (cabinet) => {
    const currentUrl = window.location.href;
    const productUrl = `${currentUrl}#cabinet-${cabinet.id}`;
    const message = `مرحباً، أريد الاستفسار عن هذه الخزانة: ${cabinet.title}\n${productUrl}`;
    
    let phoneNumber = whatsappNumber.replace(/[^0-9]/g, ''); // إزالة أي رموز غير رقمية

    // تنسيق رقم الهاتف للواتساب
    if (phoneNumber.startsWith('0966')) {
      // إذا كان الرقم يبدأ بـ 0966، احذف الصفر الأول
      phoneNumber = phoneNumber.substring(1);
    } else if (phoneNumber.startsWith('966')) {
      // إذا كان يبدأ بـ 966، اتركه كما هو
      phoneNumber = phoneNumber;
    } else if (phoneNumber.startsWith('0')) {
      // إذا كان يبدأ بـ 0، استبدله بـ 966
      phoneNumber = '966' + phoneNumber.substring(1);
    } else if (phoneNumber.length === 9) {
      // إذا كان الرقم 9 أرقام، أضف 966
      phoneNumber = '966' + phoneNumber;
    }
    
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <>
      <Helmet>
        <title>معرض الخزائن العصرية وخزائن الملابس -  عجائب الخبراء</title>
        <meta name="description" content="استكشف مجموعتنا المتنوعة من تصاميم الخزائن العصرية والكلاسيكية وخزائن الملابس الفاخرة. أحدث تصاميم الخزائن في السعودية مع عجائب الخبراء" />
        <meta name="keywords" content="خزائن عصرية, خزائن كلاسيكية, خزائن ملابس, خزائن فاخرة, تصاميم خزائن, معرض خزائن, تفصيل خزائن, عجائب الخبراء" />
        <link rel="canonical" href="https://khobrakitchens.com/cabinets" />

        {/* Structured Data */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            "name": "معرض الخزائن العصرية وخزائن الملابس",
            "description": "استكشف مجموعتنا المتنوعة من تصاميم الخزائن العصرية والكلاسيكية وخزائن الملابس الفاخرة",
            "url": "https://khobrakitchens.com/cabinets",
            "mainEntity": {
              "@type": "ItemList",
              "name": "مجموعة الخزائن",
              "numberOfItems": filteredCabinets.length,
              "itemListElement": filteredCabinets.slice(0, 10).map((cabinet, index) => ({
                "@type": "Product",
                "position": index + 1,
                "name": cabinet.title,
                "description": cabinet.description,
                "image": cabinet.images[0],
                "category": categories.find(cat => cat.id === cabinet.category)?.name || 'عصري'
              }))
            }
          })}
        </script>
      </Helmet>
      
      <Navbar />
      
      <section className="relative py-24 bg-gradient-to-br from-slate-50 via-white to-purple-50 overflow-hidden" ref={sectionRef}>
        {/* Background Decorations */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-200/20 to-blue-200/20 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-pink-200/20 to-purple-200/20 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          {/* Header */}
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl mb-6 shadow-lg">
              <i className="ri-archive-line text-2xl text-white"></i>
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-800 via-gray-900 to-gray-800 bg-clip-text text-transparent mb-6">
              معرض
              <span className="block bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent">
                الخزائن الأنيقة
              </span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              استكشف مجموعتنا الكاملة من تصاميم الخزائن العصرية والكلاسيكية التي تجمع بين الجمال والوظيفة
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-blue-500 mx-auto mt-8 rounded-full"></div>
          </motion.div>

          {/* Category Filter - Hidden */}
          {false && (
          <motion.div
            className="flex flex-wrap justify-center gap-4 mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center space-x-2 rtl:space-x-reverse px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  selectedCategory === category.id
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg transform scale-105'
                    : 'bg-white text-gray-700 hover:bg-gray-50 shadow-md hover:shadow-lg'
                }`}
              >
                <i className={`${category.icon} text-lg`}></i>
                <span>{category.name}</span>
              </button>
            ))}
          </motion.div>
          )}

          {/* Cabinets Grid */}
          {filteredCabinets.length > 0 ? (
            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {filteredCabinets.map((cabinet, index) => (
                <motion.div
                  key={cabinet.id}
                  className="group relative bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer"
                  whileHover={{ y: -10 }}
                  onClick={() => setLightboxImage(cabinet)}
                  initial={{ opacity: 0, y: 30 }}
                  animate={isInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <div className="relative h-80 md:h-96 overflow-hidden">
                    <img
                      src={cabinet.images[0]}
                      alt={cabinet.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-xs font-medium">
                      {categories.find(cat => cat.id === cabinet.category)?.name || 'عصري'}
                    </div>
                    <div className="absolute bottom-4 left-4 right-4 text-white transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                      <h3 className="text-xl font-bold mb-2">{cabinet.title}</h3>
                      <p className="text-sm text-gray-200 line-clamp-2">{cabinet.description}</p>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse text-gray-500">
                        <i className="ri-image-line"></i>
                        <span className="text-sm">{cabinet.images.length} صورة</span>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          openWhatsApp(cabinet);
                        }}
                        className="flex items-center space-x-2 rtl:space-x-reverse bg-green-500 text-white px-4 py-2 rounded-full hover:bg-green-600 transition-colors duration-300"
                      >
                        <i className="ri-whatsapp-line"></i>
                        <span className="text-sm">استفسار</span>
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          ) : (
            <motion.div
              className="text-center py-16"
              initial={{ opacity: 0 }}
              animate={isInView ? { opacity: 1 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="ri-archive-line text-3xl text-gray-400"></i>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">لا توجد خزائن</h3>
              <p className="text-gray-600 mb-6">لم يتم العثور على خزائن في هذه الفئة</p>
            </motion.div>
          )}
        </div>
      </section>

      {/* Enhanced Modal - نسخة من الصفحة الرئيسية مع ألوان الخزائن */}
      <AnimatePresence>
        {lightboxImage && (
          <motion.div
            className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => {
              setLightboxImage(null);
              setModalImageIndex(0);
            }}
          >
            {/* Mobile Modal */}
            <motion.div
              className="lg:hidden relative bg-white w-full h-full flex flex-col shadow-2xl"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Mobile Close Button */}
              <motion.button
                className="absolute top-4 right-4 z-20 w-12 h-12 bg-black/50 backdrop-blur-sm text-white rounded-full flex items-center justify-center"
                onClick={() => {
                  setLightboxImage(null);
                  setModalImageIndex(0);
                }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <i className="ri-close-line text-2xl"></i>
              </motion.button>

              {/* Mobile Image Slider */}
              <div className="relative flex-1 bg-black">
                <Swiper
                  modules={[Navigation, Pagination]}
                  spaceBetween={0}
                  slidesPerView={1}
                  navigation={{
                    nextEl: '.mobile-swiper-button-next',
                    prevEl: '.mobile-swiper-button-prev',
                  }}
                  pagination={{
                    clickable: true,
                    dynamicBullets: true,
                  }}
                  onSlideChange={(swiper) => setModalImageIndex(swiper.activeIndex)}
                  initialSlide={modalImageIndex}
                  className="mobile-modal-swiper h-full"
                  style={{
                    '--swiper-pagination-color': '#8b5cf6',
                    '--swiper-pagination-bullet-inactive-color': '#d1d5db',
                  }}
                >
                  {lightboxImage.images.map((image, index) => (
                    <SwiperSlide key={index}>
                      <div className="relative h-full flex items-center justify-center bg-gradient-to-br from-gray-100 via-white to-gray-50 p-4">
                        <img
                          src={image}
                          alt={`${lightboxImage.title} - صورة ${index + 1}`}
                          className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
                        />
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>

                {/* Mobile Navigation Buttons */}
                <div className="mobile-swiper-button-prev absolute left-4 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:bg-white/30 transition-all duration-300">
                  <i className="ri-arrow-right-line text-lg text-white"></i>
                </div>
                <div className="mobile-swiper-button-next absolute right-4 top-1/2 -translate-y-1/2 z-10 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg cursor-pointer hover:bg-white/30 transition-all duration-300">
                  <i className="ri-arrow-left-line text-lg text-white"></i>
                </div>

                {/* Mobile Image Counter */}
                {lightboxImage.images.length > 1 && (
                  <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium">
                    {modalImageIndex + 1} / {lightboxImage.images.length}
                  </div>
                )}
              </div>

              {/* Mobile Content Section */}
              <div className="p-6 bg-white max-h-[40vh] overflow-y-auto">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">
                    {lightboxImage.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed text-base">
                    {lightboxImage.description}
                  </p>
                </div>

                {/* Mobile Contact Section */}
                <div className="text-center">
                  <h4 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-6">
                    اطلب الآن
                  </h4>

                  {socialMedia.length > 0 && (
                    <div className="flex justify-center gap-3 mb-6">
                      {socialMedia.map((social, index) => {
                        if (social.platform === 'whatsapp') {
                          return (
                            <motion.button
                              key={index}
                              onClick={() => openWhatsApp(lightboxImage)}
                              className={`flex items-center justify-center w-12 h-12 text-white rounded-full transition-colors duration-300 ${getBackgroundClass(social.platform)}`}
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                            >
                              <i className={`${social.icon} text-lg`}></i>
                            </motion.button>
                          );
                        }
                        return (
                          <motion.a
                            key={index}
                            href={social.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className={`flex items-center justify-center w-12 h-12 text-white rounded-full transition-colors duration-300 ${getBackgroundClass(social.platform)}`}
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                          >
                            <i className={`${social.icon} text-lg`}></i>
                          </motion.a>
                        );
                      })}
                    </div>
                  )}

                  {/* Mobile Thumbnail Images */}
                  {lightboxImage.images.length > 1 && (
                    <div className="mb-6">
                      <div className="grid grid-cols-3 gap-2">
                        {lightboxImage.images.map((image, index) => (
                          <button
                            key={index}
                            onClick={() => setModalImageIndex(index)}
                            className={`relative h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                              modalImageIndex === index
                                ? 'border-purple-500 scale-105'
                                : 'border-gray-200 hover:border-purple-300'
                            }`}
                          >
                            <img
                              src={image}
                              alt={`${lightboxImage.title} - صورة ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>

            {/* Desktop Modal */}
            <motion.div
              className="hidden lg:flex relative bg-white rounded-3xl overflow-hidden max-w-7xl w-full max-h-[90vh] shadow-2xl"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >

              {/* Desktop Close Button */}
              <motion.button
                className="absolute top-6 right-6 z-20 w-12 h-12 bg-black/50 backdrop-blur-sm text-white rounded-full flex items-center justify-center hover:bg-black/70 transition-colors duration-300"
                onClick={() => {
                  setLightboxImage(null);
                  setModalImageIndex(0);
                }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <i className="ri-close-line text-xl"></i>
              </motion.button>

              {/* Desktop Image Section */}
              <div className="flex-[0.65] relative bg-gradient-to-br from-gray-100 via-white to-gray-50 flex items-center justify-center">
                <img
                  src={lightboxImage.images[modalImageIndex]}
                  alt={`${lightboxImage.title} - صورة ${modalImageIndex + 1}`}
                  className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
                />

                {/* Desktop Image Counter */}
                {lightboxImage.images.length > 1 && (
                  <div className="absolute bottom-6 left-6 bg-black/70 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium">
                    {modalImageIndex + 1} / {lightboxImage.images.length}
                  </div>
                )}
              </div>

              {/* Desktop Content Section */}
              <div className="flex-[0.35] p-8 bg-gradient-to-br from-gray-50 to-white flex flex-col justify-between">
                <div>
                  <div className="text-center mb-8">
                    <h3 className="text-3xl font-bold text-gray-800 mb-6">
                      {lightboxImage.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed text-lg">
                      {lightboxImage.description}
                    </p>
                  </div>

                  {/* Desktop Contact Section */}
                  <div className="text-center">
                    <h4 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-8">
                      اطلب الآن
                    </h4>

                    {socialMedia.length > 0 && (
                      <div className="flex justify-center gap-4 mb-8">
                        {socialMedia.map((social, index) => {
                          if (social.platform === 'whatsapp') {
                            return (
                              <motion.button
                                key={index}
                                onClick={() => openWhatsApp(lightboxImage)}
                                className={`flex items-center justify-center w-16 h-16 text-white rounded-full transition-colors duration-300 shadow-lg ${getBackgroundClass(social.platform)}`}
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <i className={`${social.icon} text-2xl`}></i>
                              </motion.button>
                            );
                          }
                          return (
                            <motion.a
                              key={index}
                              href={social.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className={`flex items-center justify-center w-16 h-16 text-white rounded-full transition-colors duration-300 shadow-lg ${getBackgroundClass(social.platform)}`}
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                            >
                              <i className={`${social.icon} text-2xl`}></i>
                            </motion.a>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>

                {/* Desktop Thumbnail Images */}
                {lightboxImage.images.length > 1 && (
                  <div className="mt-6">
                    <div className="grid grid-cols-3 gap-3">
                      {lightboxImage.images.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setModalImageIndex(index)}
                          className={`relative h-20 rounded-xl overflow-hidden border-3 transition-all duration-300 ${
                            modalImageIndex === index
                              ? 'border-purple-500 scale-105 shadow-lg'
                              : 'border-gray-200 hover:border-purple-300 hover:scale-102'
                          }`}
                        >
                          <img
                            src={image}
                            alt={`${lightboxImage.title} - صورة ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default CabinetsPage;
