import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay, Navigation } from 'swiper/modules';
import { getKitchensData, getFooterData } from '../../../database/api-client.js';
import { getImageURL } from '../../config/api.js';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';

const MobileKitchensPage = () => {
  const navigate = useNavigate();
  const sectionRef = useRef(null);

  const [kitchens, setKitchens] = useState([]);
  const [whatsappNumber, setWhatsappNumber] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedKitchen, setSelectedKitchen] = useState(null);
  const [modalImageIndex, setModalImageIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // استخدام useInView فقط عندما لا نكون في حالة تحميل أو خطأ
  const isInView = useInView(sectionRef, { once: true, threshold: 0.1 });

  // فئات المطابخ
  const categories = [
    { id: 'all', name: 'الكل', icon: 'ri-apps-line', color: 'from-blue-500 to-purple-500' },
    { id: 'modern-kitchens', name: 'عصري', icon: 'ri-building-line', color: 'from-green-500 to-emerald-500' },
    { id: 'classic-kitchens', name: 'كلاسيكي', icon: 'ri-home-heart-line', color: 'from-orange-500 to-red-500' },
    { id: 'luxury-kitchens', name: 'فاخر', icon: 'ri-vip-crown-line', color: 'from-purple-500 to-pink-500' }
  ];

  // تحميل البيانات
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // تحميل المطابخ
        const kitchensData = await getKitchensData();
        if (kitchensData && kitchensData.length > 0) {
          const formattedKitchens = kitchensData.map(kitchen => ({
            id: kitchen.id,
            title: kitchen.title,
            description: kitchen.description,
            category: kitchen.category_slug || 'modern-kitchens',
            images: kitchen.images?.map(img => {
              const imageUrl = typeof img === 'string' ? img : (img.image_url || img);
              return getImageURL(imageUrl);
            }) || []
          }));
          setKitchens(formattedKitchens);
        }

        // تحميل رقم الواتساب
        const footerData = await getFooterData();
        if (footerData?.contactInfo) {
          const phoneContact = footerData.contactInfo.find(contact => contact.icon === 'ri-phone-line');
          if (phoneContact) {
            setWhatsappNumber(phoneContact.text);
          }
        }
      } catch (error) {
        console.error('Error loading data:', error);
        setError('حدث خطأ في تحميل البيانات. يرجى المحاولة مرة أخرى.');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // تصفية المطابخ حسب الفئة
  const filteredKitchens = selectedCategory === 'all'
    ? kitchens
    : kitchens.filter(kitchen => kitchen.category === selectedCategory);

  // دالة فتح الواتساب
  const openWhatsApp = (kitchen) => {
    let phoneNumber = whatsappNumber.replace(/[^0-9]/g, '');
    if (phoneNumber.startsWith('0966')) {
      phoneNumber = phoneNumber.substring(1);
    } else if (phoneNumber.startsWith('0')) {
      phoneNumber = '966' + phoneNumber.substring(1);
    } else if (phoneNumber.length === 9) {
      phoneNumber = '966' + phoneNumber;
    }
    
    const productUrl = `https://khobrakitchens.com/kitchens`;
    const message = `مرحباً، أريد الاستفسار عن ${kitchen.title}\n${productUrl}`;
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  // فتح المودال
  const openModal = (kitchen) => {
    setSelectedKitchen(kitchen);
    setModalImageIndex(0);
  };

  // إغلاق المودال
  const closeModal = () => {
    setSelectedKitchen(null);
    setModalImageIndex(0);
  };

  // عرض شاشة التحميل أو الخطأ داخل المكون الرئيسي لتجنب مشاكل hooks
  const renderContent = () => {
    if (loading) {
      return (
        <div className="bg-gray-50 min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل المطابخ...</p>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="bg-gray-50 min-h-screen flex items-center justify-center p-4">
          <div className="text-center">
            <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="ri-error-warning-line text-3xl text-red-500"></i>
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">خطأ في التحميل</h3>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-orange-500 text-white px-6 py-3 rounded-xl font-medium"
            >
              إعادة المحاولة
            </button>
          </div>
        </div>
      );
    }

    // المحتوى الرئيسي للمطابخ
    return (
      <div className="bg-gray-50 min-h-screen" ref={sectionRef}>
        {/* Header Section */}
        <div className="bg-gradient-to-br from-orange-500 via-red-500 to-pink-600 text-white px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center"
        >
          <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="ri-restaurant-line text-3xl"></i>
          </div>
          <h1 className="text-2xl font-bold mb-2">معرض المطابخ</h1>
          <p className="text-white/90 text-sm leading-relaxed">
            اكتشف تشكيلتنا المتنوعة من المطابخ العصرية والكلاسيكية والفاخرة
          </p>
        </motion.div>
      </div>

      {/* Categories Filter */}
      <div className="px-4 py-6 bg-white">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <h3 className="text-lg font-bold text-gray-800 mb-4 text-center">اختر الفئة</h3>
          <div className="grid grid-cols-2 gap-3">
            {categories.map((category) => (
              <motion.button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`p-4 rounded-2xl border-2 transition-all duration-300 ${
                  selectedCategory === category.id
                    ? `bg-gradient-to-r ${category.color} text-white border-transparent shadow-lg`
                    : 'bg-gray-50 text-gray-700 border-gray-200 hover:border-gray-300'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex flex-col items-center space-y-2">
                  <i className={`${category.icon} text-2xl`}></i>
                  <span className="font-medium text-sm">{category.name}</span>
                </div>
              </motion.button>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Kitchens Grid */}
      <div className="px-4 pb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="space-y-4"
        >
          {filteredKitchens.length > 0 ? (
            filteredKitchens.map((kitchen, index) => (
              <motion.div
                key={kitchen.id}
                className="bg-white rounded-2xl overflow-hidden shadow-lg"
                initial={{ opacity: 0, y: 30 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
              >
                <div className="relative h-48">
                  <img
                    src={kitchen.images[0]}
                    alt={kitchen.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <h3 className="text-white font-bold text-lg mb-1">{kitchen.title}</h3>
                    <p className="text-white/90 text-sm line-clamp-2">{kitchen.description}</p>
                  </div>
                  
                  {/* View Button */}
                  <button
                    onClick={() => openModal(kitchen)}
                    className="absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white"
                  >
                    <i className="ri-eye-line text-lg"></i>
                  </button>
                </div>
                
                <div className="p-4">
                  <div className="flex space-x-3 rtl:space-x-reverse">
                    <motion.button
                      onClick={() => openWhatsApp(kitchen)}
                      className="flex-1 bg-green-500 text-white py-3 px-4 rounded-xl font-medium flex items-center justify-center space-x-2 rtl:space-x-reverse"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <i className="ri-whatsapp-line"></i>
                      <span>استفسار</span>
                    </motion.button>
                    
                    <motion.button
                      onClick={() => openModal(kitchen)}
                      className="flex-1 bg-blue-500 text-white py-3 px-4 rounded-xl font-medium flex items-center justify-center space-x-2 rtl:space-x-reverse"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <i className="ri-image-line"></i>
                      <span>عرض</span>
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            ))
          ) : (
            <div className="text-center py-12">
              <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="ri-search-line text-3xl text-gray-400"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-600 mb-2">لا توجد مطابخ</h3>
              <p className="text-gray-500 text-sm">لم يتم العثور على مطابخ في هذه الفئة</p>
            </div>
          )}
        </motion.div>
      </div>

      {/* Modal للعرض التفصيلي */}
      <AnimatePresence>
        {selectedKitchen && (
          <motion.div
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={closeModal}
          >
            <motion.div
              className="bg-white rounded-3xl max-w-sm w-full max-h-[90vh] overflow-y-auto"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="relative">
                <div className="h-64 relative overflow-hidden rounded-t-3xl">
                  <img
                    src={selectedKitchen.images[modalImageIndex]}
                    alt={selectedKitchen.title}
                    className="w-full h-full object-cover"
                  />

                  {/* Navigation Arrows */}
                  {selectedKitchen.images.length > 1 && (
                    <>
                      <button
                        onClick={() => setModalImageIndex(prev =>
                          prev === 0 ? selectedKitchen.images.length - 1 : prev - 1
                        )}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white"
                      >
                        <i className="ri-arrow-left-s-line text-xl"></i>
                      </button>

                      <button
                        onClick={() => setModalImageIndex(prev =>
                          prev === selectedKitchen.images.length - 1 ? 0 : prev + 1
                        )}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white"
                      >
                        <i className="ri-arrow-right-s-line text-xl"></i>
                      </button>
                    </>
                  )}

                  {/* Image Counter */}
                  {selectedKitchen.images.length > 1 && (
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 backdrop-blur-sm rounded-full px-3 py-1">
                      <span className="text-white text-sm">
                        {modalImageIndex + 1} / {selectedKitchen.images.length}
                      </span>
                    </div>
                  )}
                </div>

                {/* Close Button */}
                <button
                  onClick={closeModal}
                  className="absolute top-4 left-4 w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white"
                >
                  <i className="ri-close-line text-xl"></i>
                </button>
              </div>

              {/* Content */}
              <div className="p-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-3">{selectedKitchen.title}</h2>
                <p className="text-gray-600 leading-relaxed mb-6">{selectedKitchen.description}</p>

                {/* Thumbnail Images */}
                {selectedKitchen.images.length > 1 && (
                  <div className="mb-6">
                    <div className="grid grid-cols-3 gap-2">
                      {selectedKitchen.images.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setModalImageIndex(index)}
                          className={`relative h-16 rounded-xl overflow-hidden border-2 transition-all duration-300 ${
                            modalImageIndex === index
                              ? 'border-orange-500 scale-105'
                              : 'border-gray-200 hover:border-orange-300'
                          }`}
                        >
                          <img
                            src={image}
                            alt={`${selectedKitchen.title} - صورة ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="space-y-3">
                  <motion.button
                    onClick={() => openWhatsApp(selectedKitchen)}
                    className="w-full bg-green-500 text-white py-4 px-6 rounded-2xl font-medium flex items-center justify-center space-x-3 rtl:space-x-reverse"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <i className="ri-whatsapp-line text-xl"></i>
                    <span>تواصل عبر الواتساب</span>
                  </motion.button>

                  {/* Social Media Share */}
                  <div className="flex justify-center space-x-4 rtl:space-x-reverse pt-4">
                    {[
                      { icon: 'ri-twitter-x-line', color: 'bg-black', name: 'X' },
                      { icon: 'ri-facebook-line', color: 'bg-blue-600', name: 'Facebook' },
                      { icon: 'ri-instagram-line', color: 'bg-pink-500', name: 'Instagram' },
                      { icon: 'ri-linkedin-line', color: 'bg-blue-700', name: 'LinkedIn' }
                    ].map((social, index) => (
                      <motion.button
                        key={index}
                        className={`w-12 h-12 ${social.color} text-white rounded-full flex items-center justify-center`}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        title={`مشاركة على ${social.name}`}
                      >
                        <i className={`${social.icon} text-lg`}></i>
                      </motion.button>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      </div>
    );
  };

  // عرض المحتوى المناسب
  return renderContent();
};

export default MobileKitchensPage;
