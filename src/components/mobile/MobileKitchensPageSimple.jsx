import { useState, useEffect } from 'react';

const MobileKitchensPageSimple = () => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <div className="bg-gray-50 min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل المطابخ...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="bg-gradient-to-br from-orange-500 via-red-500 to-pink-600 text-white px-4 py-8">
        <div className="text-center">
          <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="ri-restaurant-line text-3xl"></i>
          </div>
          <h1 className="text-2xl font-bold mb-2">معرض المطابخ</h1>
          <p className="text-white/90 text-sm leading-relaxed">
            اكتشف تشكيلتنا المتنوعة من المطابخ العصرية والكلاسيكية والفاخرة
          </p>
        </div>
      </div>
      
      <div className="p-4">
        <div className="bg-white rounded-2xl p-6 text-center">
          <h2 className="text-xl font-bold text-gray-800 mb-2">صفحة المطابخ للهواتف</h2>
          <p className="text-gray-600">هذه نسخة مبسطة لاختبار عمل المكون</p>
        </div>
      </div>
    </div>
  );
};

export default MobileKitchensPageSimple;
